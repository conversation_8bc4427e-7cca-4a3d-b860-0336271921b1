/**
 * Common Admin JavaScript Functions
 * ฟังก์ชันที่ใช้ร่วมกันในระบบ Admin
 */

// Global variables
window.AdminCommon = {
    // Show loading state
    showLoading: function(element, text = 'กำลังโหลด...') {
        if (element) {
            element.classList.add('loading');
            const originalText = element.innerHTML;
            element.setAttribute('data-original-text', originalText);
            element.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${text}`;
            element.disabled = true;
        }
    },

    // Hide loading state
    hideLoading: function(element) {
        if (element) {
            element.classList.remove('loading');
            const originalText = element.getAttribute('data-original-text');
            if (originalText) {
                element.innerHTML = originalText;
                element.removeAttribute('data-original-text');
            }
            element.disabled = false;
        }
    },

    // Show toast notification
    showToast: function(message, type = 'success') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        `;

        // Add to body
        document.body.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    },

    // Confirm dialog
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },

    // AJAX helper
    ajax: function(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        const finalOptions = { ...defaultOptions, ...options };
        
        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
    },

    // Format number with Thai locale
    formatNumber: function(number) {
        return new Intl.NumberFormat('th-TH').format(number);
    },

    // Format currency
    formatCurrency: function(amount) {
        return '฿' + this.formatNumber(amount);
    },

    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Initialize tooltips
    initTooltips: function() {
        if (typeof $ !== 'undefined' && $.fn.tooltip) {
            $('[data-toggle="tooltip"]').tooltip();
        }
    },

    // Initialize all common features
    init: function() {
        this.initTooltips();
        this.initGlobalEventListeners();
    },

    // Global event listeners
    initGlobalEventListeners: function() {
        // Auto-dismiss alerts
        document.addEventListener('click', function(e) {
            if (e.target.matches('.alert .close')) {
                const alert = e.target.closest('.alert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, 300);
                }
            }
        });

        // Prevent double form submission
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.classList.contains('submitting')) {
                e.preventDefault();
                return false;
            }
            form.classList.add('submitting');
            
            // Remove class after 3 seconds to allow resubmission if needed
            setTimeout(() => {
                form.classList.remove('submitting');
            }, 3000);
        });
    }
};

// Bulk Actions Helper
window.BulkActions = {
    init: function(options = {}) {
        this.options = {
            selectAllSelector: '#selectAll',
            itemCheckboxSelector: '.item-checkbox',
            bulkActionsSelector: '.bulk-actions',
            selectedCountSelector: '#selectedCount',
            ...options
        };

        this.bindEvents();
    },

    bindEvents: function() {
        const selectAll = document.querySelector(this.options.selectAllSelector);
        const itemCheckboxes = document.querySelectorAll(this.options.itemCheckboxSelector);
        
        if (selectAll) {
            selectAll.addEventListener('change', () => {
                itemCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAll.checked;
                });
                this.updateBulkActions();
            });
        }

        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateBulkActions();
                this.updateSelectAll();
            });
        });
    },

    updateBulkActions: function() {
        const checkedBoxes = document.querySelectorAll(this.options.itemCheckboxSelector + ':checked');
        const count = checkedBoxes.length;
        const bulkActions = document.querySelector(this.options.bulkActionsSelector);
        const selectedCount = document.querySelector(this.options.selectedCountSelector);
        
        if (bulkActions) {
            bulkActions.style.display = count > 0 ? 'block' : 'none';
        }
        
        if (selectedCount) {
            selectedCount.textContent = count;
        }
    },

    updateSelectAll: function() {
        const selectAll = document.querySelector(this.options.selectAllSelector);
        const itemCheckboxes = document.querySelectorAll(this.options.itemCheckboxSelector);
        const checkedCount = document.querySelectorAll(this.options.itemCheckboxSelector + ':checked').length;
        
        if (selectAll) {
            selectAll.checked = checkedCount === itemCheckboxes.length;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
        }
    },

    getSelectedIds: function() {
        const checkedBoxes = document.querySelectorAll(this.options.itemCheckboxSelector + ':checked');
        return Array.from(checkedBoxes).map(cb => cb.value);
    },

    clearSelection: function() {
        const selectAll = document.querySelector(this.options.selectAllSelector);
        const itemCheckboxes = document.querySelectorAll(this.options.itemCheckboxSelector);
        
        if (selectAll) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        }
        
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        
        this.updateBulkActions();
    }
};



// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    AdminCommon.init();
});
